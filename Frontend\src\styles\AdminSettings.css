/* AdminSettings Component Styles */
.AdminSettings {
  background-color: var(--bg-gray);
  min-height: calc(100vh - 70px);
}

/* Header Section */
.AdminSettings .AdminSettings__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--light-gray);
  gap: 1rem;
}

.AdminSettings .AdminSettings__header .header-left {
  flex: 1;
  min-width: 0; /* Allow text to wrap */
}

.AdminSettings .AdminSettings__header .header-left h1 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.AdminSettings .AdminSettings__header .header-icon {
  color: var(--primary-color);
  font-size: 1.8rem;
  flex-shrink: 0;
}

.AdminSettings .AdminSettings__header .header-left p {
  color: var(--dark-gray);
  margin: 0;
  font-size: 1rem;
  line-height: 1.4;
}

.AdminSettings .AdminSettings__header .header-right {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-shrink: 0;
}

/* Loading State */
.AdminSettings .AdminSettings__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.AdminSettings .AdminSettings__loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--light-gray);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.AdminSettings .AdminSettings__loading p {
  color: var(--dark-gray);
  font-size: 1.1rem;
}

/* Form Styles */
.AdminSettings .AdminSettings__form {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

/* Settings Section */
.AdminSettings .settings-section {
  background: var(--white);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--light-gray);
}

.AdminSettings .section-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--light-gray);
}

.AdminSettings .section-header h2 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.AdminSettings .section-header .section-icon {
  color: var(--primary-color);
  font-size: 1.3rem;
}

.AdminSettings .section-header p {
  color: var(--dark-gray);
  margin: 0;
  font-size: 0.95rem;
}

/* Form Grid */
.AdminSettings .form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  align-items: start;
}

/* Form Section Title */
.AdminSettings .form-section-title {
  margin: 2rem 0 1.5rem 0;
  padding: 1rem 0;
  border-top: 1px solid var(--light-gray);
}

.AdminSettings .form-section-title h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.AdminSettings .form-section-title p {
  color: var(--dark-gray);
  margin: 0;
  font-size: 0.9rem;
}

.AdminSettings .form-group {
  display: flex;
  flex-direction: column;
}

.AdminSettings .form-group.full-width {
  grid-column: 1 / -1;
}

.AdminSettings .form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.AdminSettings .form-group label svg {
  color: var(--primary-color);
  font-size: 1rem;
}

.AdminSettings .form-control {
  padding: 0.75rem 1rem;
  border: 1px solid var(--light-gray);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background-color: var(--white);
}

.AdminSettings .form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.AdminSettings .form-control::placeholder {
  color: var(--dark-gray);
}

.AdminSettings textarea.form-control {
  resize: vertical;
  min-height: 80px;
}

.AdminSettings .form-text {
  font-size: 0.85rem;
  color: var(--dark-gray);
  margin-top: 0.25rem;
}

/* Toggle Button */
.AdminSettings .toggle-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.AdminSettings .toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid var(--light-gray);
  border-radius: 8px;
  background: var(--white);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
  width: fit-content;
}

.AdminSettings .toggle-btn:hover {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-rgb), 0.05);
}

.AdminSettings .toggle-btn.active {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-rgb), 0.1);
}

.AdminSettings .toggle-btn .toggle-on {
  color: #10b981;
  font-size: 1.2rem;
}

.AdminSettings .toggle-btn .toggle-off {
  color: var(--dark-gray);
  font-size: 1.2rem;
}

/* Button Styles */
.AdminSettings .btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
}

.AdminSettings .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.AdminSettings .btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.AdminSettings .btn-primary:hover:not(:disabled) {
  background-color: #d63c2a;
  transform: translateY(-1px);
}

.AdminSettings .btn-secondary {
  background-color: var(--white);
  color: var(--text-color);
  border: 1px solid var(--light-gray);
}

.AdminSettings .btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-gray);
  border-color: var(--primary-color);
}

/* Logo Upload Styles */
.AdminSettings .logo-upload-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.AdminSettings .current-logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.AdminSettings .logo-preview {
  max-width: 200px;
  max-height: 100px;
  border-radius: 8px;
  border: 1px solid var(--light-gray);
  object-fit: contain;
  background-color: var(--white);
  padding: 0.5rem;
}

.AdminSettings .favicon-preview {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid var(--light-gray);
  object-fit: contain;
  background-color: var(--white);
  padding: 0.25rem;
}

.AdminSettings .file-input {
  cursor: pointer;
}

.AdminSettings .file-input:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.AdminSettings .upload-progress {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  font-size: 0.9rem;
}

.AdminSettings .upload-progress .loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--light-gray);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Error State */
.AdminSettings .AdminSettings__error {
  background-color: #fef2f2;
  border: 1px solid var(--error-color);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
}

.AdminSettings .AdminSettings__error p {
  color: var(--error-color);
  margin: 0;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .AdminSettings .AdminSettings__header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .AdminSettings .AdminSettings__header .header-right {
    justify-content: flex-end;
  }

  .AdminSettings .form-grid {
    grid-template-columns: 1fr;
  }

  .AdminSettings .settings-section {
    padding: 1.5rem;
  }

  .AdminSettings .AdminSettings__header .header-left h1 {
    font-size: 1.5rem;
  }

  .AdminSettings .section-header h2 {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .AdminSettings .AdminSettings__header .header-right {
    flex-direction: column;
    gap: 0.75rem;
  }

  .AdminSettings .btn {
    width: 100%;
    justify-content: center;
  }
}

/* Social Media Section Styles */
.AdminSettings .social-media-section {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 1px solid #e1e8ff;
  border-radius: 12px;
  padding: 2rem;
  margin-top: 1rem;
}

.AdminSettings .social-media-section .form-section-title {
  margin-top: 0;
  padding-top: 0;
  border-top: none;
}

.AdminSettings .social-media-section .form-section-title h3 {
  color: #4f46e5;
}

.AdminSettings .social-input-group {
  position: relative;
}

.AdminSettings .social-input-group .form-control {
  padding-left: 3rem;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.AdminSettings .social-input-group .form-control:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.AdminSettings .social-icon-wrapper {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.AdminSettings .social-icon-wrapper svg {
  font-size: 1.1rem;
}

.AdminSettings .social-icon-wrapper.facebook svg {
  color: #1877f2;
}

.AdminSettings .social-icon-wrapper.twitter svg {
  color: #1da1f2;
}

.AdminSettings .social-icon-wrapper.instagram svg {
  color: #e4405f;
}

/* Upload Section Improvements */
.AdminSettings .upload-section {
  background: #fafbfc;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.AdminSettings .upload-section:hover {
  border-color: #4f46e5;
  background: #f8f9ff;
}

.AdminSettings .upload-section.has-file {
  border-style: solid;
  border-color: #10b981;
  background: #f0fdf4;
}

.AdminSettings .upload-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.AdminSettings .upload-icon {
  width: 48px;
  height: 48px;
  background: #e5e7eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 1.5rem;
}

.AdminSettings .upload-text {
  color: #374151;
  font-weight: 500;
}

.AdminSettings .upload-subtext {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Launch Settings Styles */
.AdminSettings .toggle-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.AdminSettings .toggle-wrapper {
  margin-bottom: 0.5rem;
}

.AdminSettings .toggle-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: var(--white);
  border: 2px solid var(--light-gray);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--dark-gray);
}

.AdminSettings .toggle-button:hover {
  border-color: var(--primary-color);
  background: var(--primary-light-color);
}

.AdminSettings .toggle-button.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
}

.AdminSettings .toggle-button svg {
  font-size: 1.5rem;
}

.AdminSettings .launch-status-info {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--light-gray);
}

.AdminSettings .status-card {
  background: var(--white);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--light-gray);
}

.AdminSettings .status-card h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: var(--heading6);
  font-weight: 600;
}

.AdminSettings .status-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  font-weight: 500;
}

.AdminSettings .status-indicator.live {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.AdminSettings .status-indicator.countdown {
  background: rgba(251, 191, 36, 0.1);
  color: #d97706;
  border: 1px solid rgba(251, 191, 36, 0.2);
}

.AdminSettings .status-indicator.maintenance {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.AdminSettings .status-indicator svg {
  font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .AdminSettings .AdminSettings__header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .AdminSettings .AdminSettings__header .header-right {
    justify-content: flex-start;
  }

  .AdminSettings .form-grid {
    grid-template-columns: 1fr;
  }

  .AdminSettings .social-input-group {
    flex-direction: column;
  }

  .AdminSettings .social-icon-wrapper {
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: 1px solid var(--light-gray);
  }

  .AdminSettings .social-input-group input {
    border-radius: 0 0 var(--border-radius) var(--border-radius);
  }

  .AdminSettings .toggle-button {
    padding: 0.5rem 0.75rem;
    font-size: var(--smallfont);
  }

  .AdminSettings .status-indicator {
    padding: 0.5rem 0.75rem;
    font-size: var(--smallfont);
  }
}
